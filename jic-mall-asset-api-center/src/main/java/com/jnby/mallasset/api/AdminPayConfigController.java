package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.payconfig.*;
import com.jnby.mallasset.dto.res.payconfig.AdminPayConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RequestMapping("/admin/pay/config/api")
@RestController
@Slf4j
@Api(tags = "管理员支付配置")
public class AdminPayConfigController {

    @Autowired
    private ICashierPayConfigService cashierPayConfigService;

    @PostMapping("/getDefaultStorePayConfigs")
    @ApiOperation(value = "获取默认配置")
    public ResponseResult<List<AdminPayConfigResp>> getDefaultStorePayConfigs(@RequestBody CommonRequest<AdminPayDefaultConfigReq> request) {
        log.info("获取默认门店支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayDefaultConfigReq req = request.getRequestData();
        List<AdminPayConfigResp> result = cashierPayConfigService.getDefaultStorePayConfigs(req);
        log.info("获取默认门店支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result, request.getPage());
    }

    @PostMapping("/searchStorePayConfigs")
    @ApiOperation(value = "门店聚合列表")
    public ResponseResult<List<AdminPayStoreConfigSearchResp>> searchStorePayConfigs(@RequestBody CommonRequest<AdminPayStoreConfigSearchReq> request) {
        log.info("搜索门店支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayStoreConfigSearchReq req = request.getRequestData();
        List<AdminPayStoreConfigSearchResp> result = cashierPayConfigService.searchStorePayConfigs(req, request.getPage());
        log.info("搜索门店支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result, request.getPage());
    }

    @PostMapping("/saveOrUpdatePayConfig")
    @ApiOperation(value = "新增或编辑支付配置")
    public ResponseResult<String> saveOrUpdatePayConfig(@RequestBody @Valid CommonRequest<AdminPayConfigSaveReq> request) {
        log.info("新增或编辑支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayConfigSaveReq req = request.getRequestData();
        String id = cashierPayConfigService.saveOrUpdatePayConfig(req, request.getUser_id());
        log.info("新增或编辑支付配置请求 回参:{}", JSON.toJSONString(id));
        return ResponseResult.success(id);
    }

    @PostMapping("/batchSaveOrUpdatePayConfig")
    @ApiOperation(value = "批量新增或编辑支付配置")
    public ResponseResult<Boolean> batchSaveOrUpdatePayConfig(@RequestBody @Valid CommonRequest<List<AdminPayConfigSaveReq>> request) {
        log.info("批量新增或编辑支付配置请求 入参:{}", JSON.toJSONString(request));
        List<AdminPayConfigSaveReq> req = request.getRequestData();
        Boolean result = cashierPayConfigService.batchSaveOrUpdatePayConfig(req, request.getUser_id());
        log.info("批量新增或编辑支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result);
    }

    @PostMapping("/getPayConfigsByStoreId")
    @ApiOperation(value = "根据门店ID查询所有支付配置")
    public ResponseResult<List<AdminPayConfigResp>> getPayConfigsByStoreId(@RequestBody @Valid CommonRequest<AdminPayConfigByStoreReq> request) {
        log.info("根据门店ID查询支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayConfigByStoreReq req = request.getRequestData();
        List<AdminPayConfigResp> result = cashierPayConfigService.getPayConfigsByStoreId(req);
        log.info("根据门店ID查询支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result);
    }

    @PostMapping("/deletePayConfig")
    @ApiOperation(value = "删除支付配置")
    public ResponseResult<String> deletePayConfig(@RequestBody @Valid CommonRequest<AdminPayConfigDeleteReq> request) {
        log.info("删除支付配置 入参:{}", JSON.toJSONString(request));
        AdminPayConfigDeleteReq req = request.getRequestData();
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getId()), "主键ID不能为空");
        String id = cashierPayConfigService.deletePayConfig(req, request.getUser_id());
        log.info("删除支付配置 回参:{}", JSON.toJSONString(id));
        return ResponseResult.success(id);
    }
}
