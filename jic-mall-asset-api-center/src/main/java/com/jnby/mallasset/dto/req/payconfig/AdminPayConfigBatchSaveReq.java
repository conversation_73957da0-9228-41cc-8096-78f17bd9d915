package com.jnby.mallasset.dto.req.payconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量新增或编辑支付配置请求
 */
@Data
@ApiModel("批量新增或编辑支付配置请求")
public class AdminPayConfigBatchSaveReq {

    @ApiModelProperty(value = "支付配置列表", required = true)
    @NotEmpty(message = "支付配置列表不能为空")
    @Valid
    private List<AdminPayConfigSaveReq> payConfigs;

}
