package com.jnby.mallasset.dto.req.payconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 根据门店ID查询支付配置请求
 */
@Data
@ApiModel("根据门店ID查询支付配置请求")
public class AdminPayConfigByStoreReq {

    @ApiModelProperty(value = "伯俊门店CODE", required = true)
    @NotBlank(message = "伯俊门店CODE不能为空")
    private String bjStoreId;
}
