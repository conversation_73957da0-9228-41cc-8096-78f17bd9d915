package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.mallasset.config.IdConfig;
import com.jnby.mallasset.config.exception.MallException;
import com.jnby.mallasset.convert.AdminPayConfigConvertor;
import com.jnby.mallasset.dto.req.payconfig.*;
import com.jnby.mallasset.dto.res.payconfig.AdminPayConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.mapper.box.CashierPayConfigMapper;
import com.jnby.mallasset.module.model.CashierPayConfig;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CashierPayConfigServiceImpl extends ServiceImpl<CashierPayConfigMapper, CashierPayConfig> implements ICashierPayConfigService {

    @Resource
    private IdConfig idConfig;

    private static final String DEFAULT_STORE_ID = "DEFAULT";

    @Override
    public List<AdminPayConfigResp> getDefaultStorePayConfigs(AdminPayDefaultConfigReq req) {
        log.info("获取默认门店支付配置 入参:{}", JSON.toJSONString(req));

        LambdaQueryWrapper<CashierPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashierPayConfig::getIsDelete, 0)
                .eq(CashierPayConfig::getBjStoreId, DEFAULT_STORE_ID);

        // 根据业务类型过滤
        if (req.getBusinessType() != null) {
            queryWrapper.eq(CashierPayConfig::getBusinessType, req.getBusinessType());
        }

        // 根据支付平台过滤
        if (req.getPayChannel() != null) {
            queryWrapper.eq(CashierPayConfig::getPayChannel, req.getPayChannel());
        }

        queryWrapper.orderByDesc(CashierPayConfig::getCreateTime);

        List<CashierPayConfig> configs = this.list(queryWrapper);
        List<AdminPayConfigResp> respList = Optional.of(configs).orElse(new ArrayList<>()).stream()
                .map(AdminPayConfigConvertor.INSTANCE::toDefaultConfig)
                .collect(Collectors.toList());

        log.info("获取默认门店支付配置 回参:{}", JSON.toJSONString(respList));
        return respList;
    }

    @Override
    public List<AdminPayStoreConfigSearchResp> searchStorePayConfigs(AdminPayStoreConfigSearchReq req, Page page) {
        log.info("搜索门店支付配置 入参:{}", JSON.toJSONString(req));
        QueryWrapper<CashierPayConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct(BJ_STORE_ID) AS bjStoreId, CREATE_TIME AS createTime, STORE_NAME AS storeName");
        queryWrapper.lambda().eq(CashierPayConfig::getIsDelete, 0);
        // 精确匹配伯俊门店CODE
        if (StringUtils.isNotBlank(req.getBjStoreId())) {
            queryWrapper.lambda().eq(CashierPayConfig::getBjStoreId, req.getBjStoreId());
        }
        // 模糊匹配门店名称
        if (StringUtils.isNotBlank(req.getStoreName())) {
            queryWrapper.lambda().like(CashierPayConfig::getStoreName, req.getStoreName());
        }
        // 支付平台过滤
        if (req.getPayChannel() != null) {
            queryWrapper.lambda().eq(CashierPayConfig::getPayChannel, req.getPayChannel());
        }
        queryWrapper.lambda().ne(CashierPayConfig::getBjStoreId, DEFAULT_STORE_ID);
        queryWrapper.lambda().orderByDesc(CashierPayConfig::getCreateTime);

        com.github.pagehelper.Page<CashierPayConfig> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<CashierPayConfig> configs = this.list(queryWrapper);
        PageInfo<CashierPayConfig> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }

        // 根据门店CODE查询详细数据
        List<String> storeIds = configs.stream().map(CashierPayConfig::getBjStoreId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<CashierPayConfig> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(CashierPayConfig::getIsDelete, 0)
                .in(CashierPayConfig::getBjStoreId, storeIds);
        log.info("搜索门店支付配置 入参:{}", storeIds);
        List<CashierPayConfig> detailConfigs = this.list(detailQueryWrapper);
        log.info("搜索门店支付配置 回参总数:{}", detailConfigs.size());

        // 按门店聚合数据
        Map<String, List<CashierPayConfig>> storeGroupMap = Optional.of(detailConfigs).orElse(Lists.newArrayList())
                .stream().collect(Collectors.groupingBy(CashierPayConfig::getBjStoreId));

        List<AdminPayStoreConfigSearchResp> respList = configs.stream().map(config ->
                        AdminPayConfigConvertor.INSTANCE.toStoreConfigSearchResp(config, storeGroupMap.get(config.getBjStoreId())))
                .collect(Collectors.toList());

        // 对 respList.payChannels 的数据进行去重
        respList.forEach(resp -> {
            if (CollectionUtils.isNotEmpty(resp.getPayChannels())) {
                resp.setPayChannels(resp.getPayChannels().stream().distinct().collect(Collectors.toList()));
            }
        });

        log.info("搜索门店支付配置 回参总数:{}", respList);
        return respList;
    }

    @Override
    public String saveOrUpdatePayConfig(AdminPayConfigSaveReq req, String optPerson) {
        Date now = new Date();
        String id = req.getId();
        if (StringUtils.isNotBlank(id)) {
            // 编辑模式：根据ID查询现有记录
            CashierPayConfig dbConfig = this.getById(id);
            if (dbConfig == null) {
                throw new MallException("支付配置不存在，ID: " + id);
            }

            // 更新字段(只能更新支付配置ID)
            CashierPayConfig updateConfig = new CashierPayConfig();
            updateConfig.setId(id);
            updateConfig.setPayConfigId(req.getPayConfigId());
            updateConfig.setUpdateTime(now);
            updateConfig.setUpdateBy(optPerson);
            log.info("更新支付配置ID: {}", JSON.toJSONString(updateConfig));
            this.updateById(updateConfig);
        } else {
            // 新增模式：检查是否存在重复配置
            LambdaQueryWrapper<CashierPayConfig> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(CashierPayConfig::getIsDelete, 0)
                    .eq(CashierPayConfig::getBjStoreId, req.getBjStoreId())
                    .eq(CashierPayConfig::getBusinessType, req.getBusinessType())
                    .eq(CashierPayConfig::getPayChannel, req.getPayChannel());

            if (this.count(checkWrapper) > 0) {
                throw new MallException("该门店的业务类型和支付平台组合已存在配置");
            }

            // 创建新记录
            CashierPayConfig config = AdminPayConfigConvertor.INSTANCE.toEntity(req);
            id = idConfig.getPayConfigId();
            config.setId(id);
            config.setIsDelete(0);
            config.setCreateTime(now);
            config.setCreateBy(optPerson);
            config.setUpdateTime(now);
            config.setUpdateBy(optPerson);
            log.info("新增支付配置ID: {}", JSON.toJSONString(config));
            this.save(config);
        }

        return id;
    }

    @Override
    public List<AdminPayConfigResp> getPayConfigsByStoreId(AdminPayConfigByStoreReq req) {
        LambdaQueryWrapper<CashierPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashierPayConfig::getIsDelete, 0)
                .eq(CashierPayConfig::getBjStoreId, req.getBjStoreId())
                .orderByDesc(CashierPayConfig::getCreateTime);
        List<CashierPayConfig> configs = this.list(queryWrapper);
        return Optional.of(configs).orElse(new ArrayList<>()).stream()
                .map(AdminPayConfigConvertor.INSTANCE::toDefaultConfig)
                .collect(Collectors.toList());
    }

    @Override
    public String deletePayConfig(AdminPayConfigDeleteReq req, String optPerson) {
        CashierPayConfig config = new CashierPayConfig();
        config.setId(req.getId());
        config.setIsDelete(1);
        config.setUpdateTime(new Date());
        config.setUpdateBy(optPerson);
        this.updateById(config);
        return req.getId();
    }
}
