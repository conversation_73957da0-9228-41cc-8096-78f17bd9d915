package com.jnby.mallasset.module.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.Page;
import com.jnby.mallasset.dto.req.payconfig.*;
import com.jnby.mallasset.dto.res.payconfig.AdminPayConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.model.CashierPayConfig;

import java.util.List;

/**
 * 收银台支付配置
 */
public interface ICashierPayConfigService extends IService<CashierPayConfig> {

    /**
     * 获取门店为DEFAULT的所有业务类型、支付平台的数据
     */
    List<AdminPayConfigResp> getDefaultStorePayConfigs(AdminPayDefaultConfigReq req);

    /**
     * 根据条件聚合搜索分页查询门店支付配置
     */
    List<AdminPayStoreConfigSearchResp> searchStorePayConfigs(AdminPayStoreConfigSearchReq req, Page page);

    /**
     * 新增或编辑支付配置
     */
    String saveOrUpdatePayConfig(AdminPayConfigSaveReq req, String optPerson);

    /**
     * 批量新增或编辑支付配置
     */
    Boolean batchSaveOrUpdatePayConfig(List<AdminPayConfigSaveReq> req, String optPerson);

    /**
     * 根据门店ID查询所有支付配置
     */
    List<AdminPayConfigResp> getPayConfigsByStoreId(AdminPayConfigByStoreReq req);

    String deletePayConfig(AdminPayConfigDeleteReq req, String optPerson);
}
