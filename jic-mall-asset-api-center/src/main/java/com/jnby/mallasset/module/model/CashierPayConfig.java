package com.jnby.mallasset.module.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2025-06-11 16:32:19
 * @Description: 收银台支付配置（门店与支付的关联）
 */
@Data
@TableName("CASHIER_PAY_CONFIG")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CashierPayConfig对象", description="收银台支付配置")
public class CashierPayConfig implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "0未删除、1已删除")
    @TableField("IS_DELETE")
    private Integer isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_BY")
    private String createBy;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "更新人")
    @TableField(value = "UPDATE_BY")
    private String updateBy;
    @ApiModelProperty(value = "伯俊门店CODE")
    @TableField("BJ_STORE_ID")
    private String bjStoreId;
    @ApiModelProperty(value = "门店名称")
    @TableField("STORE_NAME")
    private String storeName;
    @ApiModelProperty(value = "业务类型：1=微商城、2=BOX、3=POS+、4=复购计划、5=POS+线上（离店）、6=储值卡")
    @TableField("BUSINESS_TYPE")
    private Integer businessType;
    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    @TableField("PAY_CHANNEL")
    private Integer payChannel;
    @ApiModelProperty(value = "支付配置ID：对应支付中心的不同平台配置表的ID")
    @TableField("PAY_CONFIG_ID")
    private String payConfigId;
    @ApiModelProperty(value = "设备号")
    @TableField("DEVICE_NUMBER")
    private String deviceNumber;
}
