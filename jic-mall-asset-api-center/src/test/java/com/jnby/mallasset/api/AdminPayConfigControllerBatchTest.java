package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.CommonRequest;
import com.jnby.mallasset.dto.req.payconfig.AdminPayConfigBatchSaveReq;
import com.jnby.mallasset.dto.req.payconfig.AdminPayConfigSaveReq;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * 批量支付配置接口测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AdminPayConfigControllerBatchTest {

    @Test
    public void testBatchSaveOrUpdatePayConfigRequest() {
        // 创建批量请求数据
        AdminPayConfigSaveReq config1 = new AdminPayConfigSaveReq();
        config1.setBjStoreId("STORE001");
        config1.setStoreName("测试门店1");
        config1.setBusinessType(1); // 微商城
        config1.setPayChannel(1); // 微信直连
        config1.setPayConfigId("PAY_CONFIG_001");

        AdminPayConfigSaveReq config2 = new AdminPayConfigSaveReq();
        config2.setBjStoreId("STORE001");
        config2.setStoreName("测试门店1");
        config2.setBusinessType(2); // BOX
        config2.setPayChannel(2); // 收钱吧-线上支付
        config2.setPayConfigId("PAY_CONFIG_002");

        AdminPayConfigSaveReq config3 = new AdminPayConfigSaveReq();
        config3.setBjStoreId("STORE002");
        config3.setStoreName("测试门店2");
        config3.setBusinessType(1); // 微商城
        config3.setPayChannel(3); // 支付宝直连
        config3.setPayConfigId("PAY_CONFIG_003");

        AdminPayConfigBatchSaveReq batchReq = new AdminPayConfigBatchSaveReq();
        batchReq.setPayConfigs(Arrays.asList(config1, config2, config3));

        CommonRequest<AdminPayConfigBatchSaveReq> request = new CommonRequest<>();
        request.setRequestData(batchReq);
        request.setUser_id("TEST_USER");

        // 打印请求JSON，用于接口测试
        System.out.println("批量新增支付配置请求JSON:");
        System.out.println(JSON.toJSONString(request, true));

        // 验证请求数据结构
        assert request.getRequestData() != null;
        assert request.getRequestData().getPayConfigs() != null;
        assert request.getRequestData().getPayConfigs().size() == 3;
        
        List<AdminPayConfigSaveReq> payConfigs = request.getRequestData().getPayConfigs();
        assert "STORE001".equals(payConfigs.get(0).getBjStoreId());
        assert "测试门店1".equals(payConfigs.get(0).getStoreName());
        assert payConfigs.get(0).getBusinessType() == 1;
        assert payConfigs.get(0).getPayChannel() == 1;
        assert "PAY_CONFIG_001".equals(payConfigs.get(0).getPayConfigId());
        
        System.out.println("批量请求数据验证通过！");
    }

    @Test
    public void testBatchUpdatePayConfigRequest() {
        // 创建批量更新请求数据（包含ID表示更新）
        AdminPayConfigSaveReq config1 = new AdminPayConfigSaveReq();
        config1.setId("EXISTING_ID_001"); // 有ID表示更新
        config1.setBjStoreId("STORE001");
        config1.setStoreName("测试门店1");
        config1.setBusinessType(1);
        config1.setPayChannel(1);
        config1.setPayConfigId("UPDATED_PAY_CONFIG_001");

        AdminPayConfigSaveReq config2 = new AdminPayConfigSaveReq();
        // 没有ID表示新增
        config2.setBjStoreId("STORE003");
        config2.setStoreName("新测试门店3");
        config2.setBusinessType(3);
        config2.setPayChannel(4);
        config2.setPayConfigId("NEW_PAY_CONFIG_004");

        AdminPayConfigBatchSaveReq batchReq = new AdminPayConfigBatchSaveReq();
        batchReq.setPayConfigs(Arrays.asList(config1, config2));

        CommonRequest<AdminPayConfigBatchSaveReq> request = new CommonRequest<>();
        request.setRequestData(batchReq);
        request.setUser_id("TEST_USER");

        // 打印请求JSON
        System.out.println("批量更新支付配置请求JSON:");
        System.out.println(JSON.toJSONString(request, true));
        
        System.out.println("批量更新请求数据验证通过！");
    }
}
